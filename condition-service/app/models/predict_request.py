from pydantic import BaseModel, Field, field_validator, model_validator
from typing import List, Optional, Any, Literal

class Condition(BaseModel):
    """
    Defines the expected condition parameters for different parts of the artwork.
    """
    support: List[str] = Field(..., description="List of condition checks for the artwork support.", min_length=1)
    frame: List[str] = Field(..., description="List of condition checks for the artwork frame.", min_length=1)

    @field_validator('support', 'frame')
    @classmethod
    def validate_condition_values(cls, v):
        """Validate that condition values are from allowed set"""
        allowed_conditions = {"crack", "hole", "tear", "stain", "discoloration", "warping", "good"}
        for condition in v:
            if condition not in allowed_conditions:
                raise ValueError(f"Invalid condition '{condition}'. Must be one of: {allowed_conditions}")
        return v

class Parameters(BaseModel):
    """
    Contains the analysis parameters for the artwork.
    """
    condition: Condition
    type: Literal["painting", "sculpture", "photograph", "drawing"] = Field(..., description="Type of artwork")

class ArtDetails(BaseModel):
    """
    Holds the metadata and details about the piece of art.
    """
    title: str = Field(..., min_length=1, max_length=200, description="Title of the artwork")
    artist: str = Field(..., min_length=1, max_length=100, description="Artist name")
    medium: str = Field(..., min_length=1, max_length=100, description="Medium used for the artwork")
    dimensions: str = Field(..., min_length=1, max_length=50, description="Dimensions of the artwork")
    user_id: str = Field(..., min_length=1, max_length=50, description="User identifier")

    @field_validator('dimensions')
    @classmethod
    def validate_dimensions_format(cls, v):
        """Validate dimensions format (e.g., '24x36 inches', '30x40 cm')"""
        import re
        pattern = r'^\d+(\.\d+)?x\d+(\.\d+)?\s*(inches|cm|mm|ft)$'
        if not re.match(pattern, v.lower()):
            raise ValueError("Dimensions must be in format like '24x36 inches' or '30x40 cm'")
        return v

class PredictRequest(BaseModel):
    """
    Main model for the art analysis request body. This is the model your
    FastAPI endpoint will expect.
    """
    s3_key: str = Field(..., min_length=1, description="S3 object key for the artwork image")
    bucket_name: str = Field(..., min_length=1, description="S3 bucket name")
    parameters: Parameters
    art_details: ArtDetails
    output_s3_path: str = Field(..., min_length=1, description="S3 path for output reports")
    artpiece_enclosed_in_frame: bool
    bounding_box: Optional[Any] = Field(None, description="Optional bounding box coordinates")

    @field_validator('s3_key')
    @classmethod
    def validate_s3_key(cls, v):
        """Validate S3 key format"""
        if not v.endswith(('.jpg', '.jpeg', '.png', '.tiff', '.bmp')):
            raise ValueError("S3 key must point to a valid image file (.jpg, .jpeg, .png, .tiff, .bmp)")
        return v

    @field_validator('bucket_name')
    @classmethod
    def validate_bucket_name(cls, v):
        """Validate S3 bucket name format"""
        import re
        # Basic S3 bucket name validation
        pattern = r'^[a-z0-9][a-z0-9\-]*[a-z0-9]$'
        if not re.match(pattern, v) or len(v) < 3 or len(v) > 63:
            raise ValueError("Invalid S3 bucket name format")
        return v

    @model_validator(mode='after')
    def validate_frame_consistency(self):
        """Validate that frame conditions are only specified when artwork is in frame"""
        if not self.artpiece_enclosed_in_frame and self.parameters.condition.frame:
            raise ValueError("Frame conditions cannot be specified when artpiece_enclosed_in_frame is False")
        return self