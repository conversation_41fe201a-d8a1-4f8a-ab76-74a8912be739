from pydantic import BaseModel, Field, field_validator, model_validator
from typing import List, Optional, Any, Literal
import logging

logger = logging.getLogger(__name__)

class Condition(BaseModel):
    """
    Defines the expected condition parameters for different parts of the artwork.
    """
    support: List[str] = Field(..., description="List of condition checks for the artwork support.", min_length=1)
    frame: List[str] = Field(..., description="List of condition checks for the artwork frame.", min_length=1)

    @field_validator('support', 'frame')
    @classmethod
    def validate_condition_values(cls, v):
        """Validate that condition values are from allowed set"""
        logger.debug(f"Validating condition values: {v}")
        allowed_conditions = {"crack", "hole", "tear", "stain", "discoloration", "warping", "good"}
        for condition in v:
            if condition not in allowed_conditions:
                logger.error(f"Invalid condition '{condition}' provided. Allowed conditions: {allowed_conditions}")
                raise ValueError(f"Invalid condition '{condition}'. Must be one of: {allowed_conditions}")
        logger.debug(f"Condition values validation passed: {v}")
        return v

class Parameters(BaseModel):
    """
    Contains the analysis parameters for the artwork.
    """
    condition: Condition
    type: Literal["painting", "sculpture", "photograph", "drawing"] = Field(..., description="Type of artwork")

class ArtDetails(BaseModel):
    """
    Holds the metadata and details about the piece of art.
    """
    title: str = Field(..., min_length=1, max_length=200, description="Title of the artwork")
    artist: str = Field(..., min_length=1, max_length=100, description="Artist name")
    medium: str = Field(..., min_length=1, max_length=100, description="Medium used for the artwork")
    dimensions: str = Field(..., min_length=1, max_length=50, description="Dimensions of the artwork")
    user_id: str = Field(..., min_length=1, max_length=50, description="User identifier")

    @field_validator('dimensions')
    @classmethod
    def validate_dimensions_format(cls, v):
        """Validate dimensions format (e.g., '24x36 inches', '30x40 cm')"""
        logger.debug(f"Validating dimensions format: {v}")
        import re
        pattern = r'^\d+(\.\d+)?x\d+(\.\d+)?\s*(inches|cm|mm|ft)$'
        if not re.match(pattern, v.lower()):
            logger.error(f"Invalid dimensions format: {v}. Expected format like '24x36 inches' or '30x40 cm'")
            raise ValueError("Dimensions must be in format like '24x36 inches' or '30x40 cm'")
        logger.debug(f"Dimensions format validation passed: {v}")
        return v

class PredictRequest(BaseModel):
    """
    Main model for the art analysis request body. This is the model your
    FastAPI endpoint will expect.
    """
    s3_key: str = Field(..., min_length=1, description="S3 object key for the artwork image")
    bucket_name: str = Field(..., min_length=1, description="S3 bucket name")
    parameters: Parameters
    art_details: ArtDetails
    output_s3_path: str = Field(..., min_length=1, description="S3 path for output reports")
    artpiece_enclosed_in_frame: bool
    bounding_box: Optional[Any] = Field(None, description="Optional bounding box coordinates")

    @field_validator('s3_key')
    @classmethod
    def validate_s3_key(cls, v):
        """Validate S3 key format"""
        logger.debug(f"Validating S3 key: {v}")
        if not v.endswith(('.jpg', '.jpeg', '.png', '.tiff', '.bmp')):
            logger.error(f"Invalid S3 key format: {v}. Must end with supported image extension")
            raise ValueError("S3 key must point to a valid image file (.jpg, .jpeg, .png, .tiff, .bmp)")
        logger.debug(f"S3 key validation passed: {v}")
        return v

    @field_validator('bucket_name')
    @classmethod
    def validate_bucket_name(cls, v):
        """Validate S3 bucket name format"""
        logger.debug(f"Validating S3 bucket name: {v}")
        import re
        # Basic S3 bucket name validation
        pattern = r'^[a-z0-9][a-z0-9\-]*[a-z0-9]$'
        if not re.match(pattern, v) or len(v) < 3 or len(v) > 63:
            logger.error(f"Invalid S3 bucket name: {v}. Must be 3-63 chars, lowercase alphanumeric with hyphens")
            raise ValueError("Invalid S3 bucket name format")
        logger.debug(f"S3 bucket name validation passed: {v}")
        return v

    @model_validator(mode='after')
    def validate_frame_consistency(self):
        """Validate that frame conditions are only specified when artwork is in frame"""
        logger.debug(f"Validating frame consistency. Enclosed in frame: {self.artpiece_enclosed_in_frame}, Frame conditions: {self.parameters.condition.frame}")
        if not self.artpiece_enclosed_in_frame and self.parameters.condition.frame:
            logger.error(f"Frame conditions specified ({self.parameters.condition.frame}) but artpiece_enclosed_in_frame is False")
            raise ValueError("Frame conditions cannot be specified when artpiece_enclosed_in_frame is False")
        logger.debug("Frame consistency validation passed")
        return self