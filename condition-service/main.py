from fastapi import FastAPI
import logging

from app.api.v1.router import api_router
from app.core.logging_config import setup_logging, get_logger

# Initialize logging configuration
setup_logging()
logger = get_logger(__name__)

app = FastAPI(title="Condition Service", version="0.0.1")

@app.on_event("startup")
async def startup_event():
    """Log application startup"""
    logger.info("🚀 Condition Service is starting up...")
    logger.info("FastAPI application initialized")
    logger.info("API version: 0.0.1")

@app.on_event("shutdown")
async def shutdown_event():
    """Log application shutdown"""
    logger.info("🛑 Condition Service is shutting down...")
    logger.info("Cleanup completed")

# Include API router
logger.info("Including API v1 router with prefix /api/v1")
app.include_router(api_router, prefix="/api/v1")
logger.info("✅ Application setup completed successfully")
